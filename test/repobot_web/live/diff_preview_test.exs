defmodule RepobotWeb.Live.DiffPreviewTest do
  use RepobotWeb.ConnCase, async: true

  import Phoenix.LiveViewTest
  import Repobot.Test.Fixtures
  import Mox

  alias Repobot.{Repo, SourceFiles}

  # Setup verification that all mocks were called
  setup :verify_on_exit!

  describe "diff preview with template rendering and repository settings" do
    setup do
      user = user_fixture()
      user = Repo.preload(user, :default_organization)
      organization = user.default_organization

      # Create a folder with settings
      folder =
        create_folder(%{
          name: "Test Folder",
          organization_id: organization.id,
          settings: %{
            "elixir_versions" => ["1.14.5", "1.15.7"],
            "otp_versions" => ["26.1", "26.2"],
            "app_name" => "MyApp"
          }
        })

      # Create repositories with their own settings
      repo1 =
        create_repository(%{
          owner: "owner1",
          name: "repo1",
          full_name: "owner1/repo1",
          folder_id: folder.id,
          organization_id: organization.id,
          settings: %{
            # Override folder setting
            "elixir_versions" => ["1.16.0"],
            "custom_setting" => "repo1_value"
          },
          data: %{
            "name" => "repo1",
            "full_name" => "owner1/repo1",
            "owner" => %{"login" => "owner1"},
            "description" => "Test repository 1"
          }
        })

      repo2 =
        create_repository(%{
          owner: "owner2",
          name: "repo2",
          full_name: "owner2/repo2",
          folder_id: folder.id,
          organization_id: organization.id,
          settings: %{
            "custom_setting" => "repo2_value"
          },
          data: %{
            "name" => "repo2",
            "full_name" => "owner2/repo2",
            "owner" => %{"login" => "owner2"},
            "description" => "Test repository 2"
          }
        })

      # Create a template source file that tests both root-level and nested settings access
      source_file =
        create_source_file(%{
          name: "ci.yml.liquid",
          target_path: ".github/workflows/ci.yml",
          content: """
          name: CI for {{ name }}
          on: [push, pull_request]
          jobs:
            test:
              strategy:
                matrix:
                  elixir:
                    {% for version in settings.elixir_versions -%}
                    - "{{ version }}"
                    {% endfor %}
                  otp:
                    {% for version in settings.otp_versions -%}
                    - "{{ version }}"
                    {% endfor %}
              steps:
                - name: Test {{ app_name }}
                  run: echo "Testing {{ description }}"
                - name: Custom step for {{ custom_setting }}
                  run: echo "Custom value"
                - name: Nested access test {{ settings.app_name }}
                  run: echo "Nested: {{ settings.custom_setting }}"
          """,
          is_template: true,
          organization_id: organization.id
        })

      # Associate source file with repositories
      Repobot.Repositories.add_source_file(repo1, source_file)
      Repobot.Repositories.add_source_file(repo2, source_file)

      # Create repository files for comparison
      create_repository_file(%{
        repository_id: repo1.id,
        path: ".github/workflows/ci.yml",
        content: "name: CI for repo1\n# existing content"
      })

      create_repository_file(%{
        repository_id: repo2.id,
        path: ".github/workflows/ci.yml",
        content: "name: CI for repo2\n# different content"
      })

      {:ok,
       user: user,
       folder: folder,
       repo1: repo1,
       repo2: repo2,
       source_file: source_file,
       organization: organization}
    end

    test "SourceFiles.Show renders template with repository settings in diff preview", %{
      conn: conn,
      user: user,
      source_file: source_file,
      repo1: repo1
    } do
      # Mock GitHub API calls
      Repobot.Test.GitHubMock
      |> expect(:client, fn _user -> :test_client end)
      |> expect(:get_file_status, 2, fn :test_client, _owner, _repo, _path ->
        {:ok, :exists}
      end)
      |> expect(:client, fn _user -> :test_client end)
      |> expect(:get_file_content, fn :test_client, _owner, _repo, _path ->
        {:ok, "name: CI for repo1\n# existing content", %{"sha" => "abc123"}}
      end)

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files/#{source_file}")

      # Wait for initial load to complete
      assert_eventually(fn ->
        html = render(view)
        html =~ "File exists"
      end)

      # Trigger the show_diff event
      view
      |> element("button[phx-click='show_diff'][phx-value-repository='#{repo1.full_name}']")
      |> render_click()

      # Verify the diff modal is shown
      html = render(view)
      assert html =~ "diff-container"

      # Verify the diff modal shows the correct repository
      assert html =~ "File diff for #{repo1.full_name}"
    end

    test "Folders.Show renders template with repository settings in diff preview", %{
      conn: conn,
      user: user,
      folder: folder,
      source_file: source_file,
      repo2: repo2
    } do
      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Wait for the page to load
      assert_eventually(fn ->
        html = render(view)
        html =~ source_file.name
      end)

      # Trigger the show_diff event by clicking the source file button
      view
      |> element(
        "button[phx-click='show_diff'][phx-value-source_file_id='#{source_file.id}'][phx-value-repository='#{repo2.full_name}']"
      )
      |> render_click()

      # Verify the diff modal is shown
      html = render(view)
      assert html =~ "diff-container"

      # Verify the diff modal shows the correct repository
      assert html =~ "File diff for #{repo2.full_name}"
    end

    test "both pages use the same render_template_for_repository function", %{
      source_file: source_file,
      repo1: repo1,
      repo2: repo2
    } do
      # Test that both repositories render the same template differently based on their settings
      {:ok, rendered1} = SourceFiles.render_template_for_repository(source_file, repo1)
      {:ok, rendered2} = SourceFiles.render_template_for_repository(source_file, repo2)

      # repo1 should use its overridden elixir version
      assert rendered1 =~ "- \"1.16.0\""
      refute rendered1 =~ "- \"1.14.5\""
      assert rendered1 =~ "repo1_value"

      # repo2 should use folder's elixir versions
      assert rendered2 =~ "- \"1.14.5\""
      assert rendered2 =~ "- \"1.15.7\""
      refute rendered2 =~ "- \"1.16.0\""
      assert rendered2 =~ "repo2_value"

      # Both should have folder's otp_versions and app_name
      assert rendered1 =~ "- \"26.1\""
      assert rendered1 =~ "Test MyApp"
      assert rendered2 =~ "- \"26.1\""
      assert rendered2 =~ "Test MyApp"
    end
  end
end
